package main

import (
	"fmt"
	"github.com/go-git/go-git/v6"
	"github.com/go-git/go-git/v6/plumbing/transport"
	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"os"
	"path/filepath"
	"sync"
	"time"
)

var (
	NamespaceIds map[string]string = map[string]string{
		"prod-app": "0e07a9b4-a03c-4226-85aa-272c36629ee8",
		"test":     "2df6698b-2dc1-43e4-9b18-5483b5411ec9",
		"prod":     "48850069-1a83-4996-8293-f5303ec38154",
		"staging":  "80264b14-a955-4804-a131-07a0b2b6297f",
	}
)

// 创建服务器配置
func createServerConfig() []constant.ServerConfig {
	return []constant.ServerConfig{
		*constant.NewServerConfig("mse-257ca714-nacos-ans.mse.aliyuncs.com", 8848, constant.WithContextPath("/nacos")),
	}
}

// 创建客户端配置
func createClientConfig(namespaceId string) constant.ClientConfig {
	return constant.ClientConfig{
		Endpoint:    "mse-257ca714-nacos-ans.mse.aliyuncs.com:8080",
		NamespaceId: namespaceId,
		RegionId:    "cn-zhangjiakou",
		AccessKey:   "LTAI5tJuh5eLzb8qzGzWapoX",
		SecretKey:   "******************************",
		OpenKMS:     true,
		TimeoutMs:   5000,
		LogLevel:    "debug",
		LogDir:      "./nacos/log",
		CacheDir:    "./nacos/cache",
	}
}

// 配置项结构
type ConfigItem struct {
	DataId  string
	Group   string
	Content string
}

// 获取指定命名空间的所有配置
func getAllConfigs(namespaceId string) ([]ConfigItem, error) {
	sc := createServerConfig()
	cc := createClientConfig(namespaceId)

	client, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &cc,
			ServerConfigs: sc,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("创建配置客户端失败: %v", err)
	}
	// 使用分页获取所有配置
	pageSize := 100
	pageNo := 1
	var allConfigs []ConfigItem
	return allConfigs, nil
	for {
		configs, err := client.SearchConfig(vo.SearchConfigParam{
			Search:   "blur",
			Group:    "",
			PageNo:   pageNo,
			PageSize: pageSize,
		})
		if err != nil {
			return nil, fmt.Errorf("搜索配置失败: %v", err)
		}

		// 将搜索结果转换为我们自定义的ConfigItem结构
		for _, item := range configs.PageItems {

			allConfigs = append(allConfigs, ConfigItem{
				DataId:  item.DataId,
				Group:   item.Group,
				Content: item.Content,
			})
			client.ListenConfig(vo.ConfigParam{
				DataId:           item.DataId,
				Group:            item.Group,
				Content:          "",
				Tag:              "",
				ConfigTags:       "",
				AppName:          "",
				BetaIps:          "",
				CasMd5:           "",
				Type:             "",
				SrcUser:          "",
				EncryptedDataKey: "",
				KmsKeyId:         "",
				UsageType:        "",
				OnChange:         ListenConfigCallback,
			})
		}

		// 如果已经获取了所有配置，则退出循环
		if pageNo*pageSize >= configs.TotalCount {
			break
		}

		pageNo++
	}

	return allConfigs, nil
}
func ListenConfigCallback(namespace, group, dataId, data string) {
	//返回的是namespace id
	//将data更新进入文件
}

// 保存配置到本地文件
func saveConfigToFile(namespaceName string, config ConfigItem) error {
	// 创建命名空间对应的目录
	dirPath := filepath.Join("./nacos/configs", namespaceName)
	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 文件名格式为：group+".yaml"
	fileName := config.Group + ".yaml"
	if config.Group == "" {
		fileName = "DEFAULT_GROUP.yaml"
	}
	filePath := filepath.Join(dirPath, fileName)

	// 写入文件
	err = os.WriteFile(filePath, []byte(config.Content), 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	fmt.Printf("已保存配置: %s/%s 到文件: %s\n", namespaceName, fileName, filePath)
	return nil
}

// 扫描所有命名空间的配置并保存
func scanAndSaveAllConfigs() {
	fmt.Println("开始扫描所有命名空间的配置...")

	var wg sync.WaitGroup

	// 为每个命名空间创建一个goroutine
	for namespaceName, namespaceId := range NamespaceIds {
		wg.Add(1)
		go func(name, id string) {
			defer wg.Done()

			fmt.Printf("正在扫描命名空间: %s (%s)\n", name, id)
			configs, err := getAllConfigs(id)
			if err != nil {
				fmt.Printf("获取命名空间 %s 的配置失败: %v\n", name, err)
				return
			}

			fmt.Printf("命名空间 %s 中找到 %d 个配置\n", name, len(configs))

			// 保存每个配置
			for _, config := range configs {
				err := saveConfigToFile(name, config)
				if err != nil {
					fmt.Printf("保存配置失败: %v\n", err)
				}
			}

			fmt.Printf("命名空间 %s 的配置扫描完成\n", name)
		}(namespaceName, namespaceId)
	}

	// 等待所有goroutine完成
	wg.Wait()
	fmt.Println("所有命名空间的配置扫描完成")
}

func main() {
	git.PlainClone("./", &git.CloneOptions{
		URL:               "https://github.com/dsers/nacos-config-yaml.git",
		Auth:              nil,
		RemoteName:        "",
		ReferenceName:     "",
		SingleBranch:      false,
		Mirror:            false,
		NoCheckout:        false,
		Depth:             0,
		RecurseSubmodules: 0,
		ShallowSubmodules: false,
		Progress:          nil,
		Tags:              0,
		InsecureSkipTLS:   false,
		CABundle:          nil,
		ProxyOptions: transport.ProxyOptions{
			URL: "",
		},
		Shared: false,
		Filter: "",
		Bare:   false,
	})
	// 创建配置目录
	err := os.MkdirAll("./nacos-config-yaml", 0755)
	if err != nil {
		panic(fmt.Sprintf("创建配置目录失败: %v", err))
	}

	// 立即执行一次扫描
	scanAndSaveAllConfigs()

	// 设置定时器，每小时执行一次扫描
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	fmt.Println("已启动定时扫描，每小时执行一次...")

	// 监听定时器事件
	for {
		select {
		case <-ticker.C:
			scanAndSaveAllConfigs()
		}
	}
}
