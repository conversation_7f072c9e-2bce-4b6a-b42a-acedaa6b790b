package main

import (
	"flag"
	"os"

	v1 "github.com/dsers/infra-api-protocol/api/infra-nacos-config/v1"
	"github.com/dsers/infra-kratos-common/pkg/app"
	commonserver "github.com/dsers/infra-kratos-common/pkg/server"
	"github.com/dsers/infra-nacos-config/internal/server"

	"github.com/dsers/infra-kratos-common/pkg/log"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
)

var (
	id, _		= os.Hostname()
	name		= "infra-nacos-config"
	Version		string
	configPath	string
)

func init() {
	flag.StringVar(&configPath, "conf", "", "-conf config.yaml")
}

func newApp(
	logger log.Logger,
	hs *http.Server,
	gs *grpc.Server,
	cs *server.ConfigSyncServer,
	ps commonserver.ProfilingServer,
	ms commonserver.MetricServer,
) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(name),
		kratos.Version(Version),
		kratos.Logger(logger),
		kratos.Server(
			hs,
			gs,
			cs,
			ps,
			ms,
		),
	)
}

func main() {
	flag.Parse()

	var bootstrap v1.Bootstrap
	logger, err := app.Init(
		app.WithID(id),
		app.WithName(name),
		app.WithVersion(Version),
		app.WithBootstrap(&bootstrap),
		app.WithConfig(configPath),
	)
	if err != nil {
		panic(err)
	}

	app, cleanup, err := initApp(bootstrap.GetServer(), &bootstrap, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	if err := app.Run(); err != nil {
		panic(err)
	}
}
