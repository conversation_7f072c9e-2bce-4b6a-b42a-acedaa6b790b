package biz

import (
	"context"
	"github.com/dsers/infra-nacos-config/internal/conf"
)

// NacosRepo Nacos仓库接口
type NacosRepo interface {
	GetAllConfigs(ctx context.Context) ([]conf.ConfigItem, error)
	ListenConfigChanges(ctx context.Context, callback func(namespace, group, dataId, data string)) error
	GetConfigsByNamespace(ctx context.Context, namespace string) ([]conf.ConfigItem, error)
}

// GitRepo Git仓库接口
type GitRepo interface {
	InitRepository(ctx context.Context) error
	SaveConfigToFile(ctx context.Context, config conf.ConfigItem) (bool, error)
	CommitAndPush(ctx context.Context, message string) error
	SyncAllConfigs(ctx context.Context, configs []conf.ConfigItem) error
}
