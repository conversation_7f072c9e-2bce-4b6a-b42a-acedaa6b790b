package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/dsers/infra-kratos-common/pkg/log"
	"github.com/dsers/infra-nacos-config/internal/conf"
)

// ConfigSyncUseCase 配置同步用例
type ConfigSyncUseCase struct {
	nacosRepo NacosRepo
	gitRepo   GitRepo
	config    *conf.ConfigSyncConfig
	log       *log.Helper
	stopChan  chan struct{}
	isRunning bool
}

// NewConfigSyncUseCase 创建配置同步用例
func NewConfigSyncUseCase(
	nacosRepo NacosRepo,
	gitRepo GitRepo,
	config *conf.ConfigSyncConfig,
	logger log.Logger,
) *ConfigSyncUseCase {
	return &ConfigSyncUseCase{
		nacosRepo: nacosRepo,
		gitRepo:   gitRepo,
		config:    config,
		log:       log.NewHelper(logger),
		stopChan:  make(chan struct{}),
	}
}

// Start 启动配置同步服务
func (uc *ConfigSyncUseCase) Start(ctx context.Context) error {
	if uc.isRunning {
		return fmt.Errorf("配置同步服务已经在运行")
	}

	uc.log.Info("启动配置同步服务")

	// 初始化Git仓库
	err := uc.gitRepo.InitRepository(ctx)
	if err != nil {
		return fmt.Errorf("初始化Git仓库失败: %v", err)
	}

	// 执行初始同步
	if uc.config.EnableAutoSync {
		err = uc.FullSync(ctx)
		if err != nil {
			uc.log.Errorf("初始同步失败: %v", err)
		}
	}

	// 启动配置监听
	if uc.config.EnableListener {
		err = uc.startConfigListener(ctx)
		if err != nil {
			uc.log.Errorf("启动配置监听失败: %v", err)
		}
	}

	// 启动定时扫描
	go uc.startPeriodicSync(ctx)

	uc.isRunning = true
	uc.log.Info("配置同步服务启动成功")
	return nil
}

// Stop 停止配置同步服务
func (uc *ConfigSyncUseCase) Stop() {
	if !uc.isRunning {
		return
	}

	uc.log.Info("停止配置同步服务")
	close(uc.stopChan)
	uc.isRunning = false
}

// FullSync 全量同步配置
func (uc *ConfigSyncUseCase) FullSync(ctx context.Context) error {
	uc.log.Info("开始全量同步配置")

	// 获取所有配置
	configs, err := uc.nacosRepo.GetAllConfigs(ctx)
	if err != nil {
		return fmt.Errorf("获取配置失败: %v", err)
	}

	// 同步到Git仓库
	err = uc.gitRepo.SyncAllConfigs(ctx, configs)
	if err != nil {
		return fmt.Errorf("同步到Git仓库失败: %v", err)
	}

	uc.log.Infof("全量同步完成，共同步 %d 个配置", len(configs))
	return nil
}

// IncrementalSync 增量同步单个配置
func (uc *ConfigSyncUseCase) IncrementalSync(ctx context.Context, namespace, group, dataId, content string) error {
	uc.log.Infof("开始增量同步配置: namespace=%s, group=%s, dataId=%s", namespace, group, dataId)

	// 创建配置项
	config := conf.ConfigItem{
		DataId:    dataId,
		Group:     group,
		Content:   content,
		Namespace: namespace,
	}

	// 保存到文件
	changed, err := uc.gitRepo.SaveConfigToFile(ctx, config)
	if err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	// 只有在文件内容发生变化时才提交并推送
	if changed {
		commitMessage := fmt.Sprintf("更新配置: %s/%s/%s - %s", namespace, group, dataId, time.Now().Format("2006-01-02 15:04:05"))
		err = uc.gitRepo.CommitAndPush(ctx, commitMessage)
		if err != nil {
			return fmt.Errorf("提交推送失败: %v", err)
		}
		uc.log.Infof("增量同步完成: %s/%s/%s (有变化)", namespace, group, dataId)
	} else {
		uc.log.Infof("增量同步完成: %s/%s/%s (无变化)", namespace, group, dataId)
	}

	return nil
}

// startConfigListener 启动配置监听
func (uc *ConfigSyncUseCase) startConfigListener(ctx context.Context) error {
	uc.log.Info("启动配置变化监听")

	return uc.nacosRepo.ListenConfigChanges(ctx, func(namespace, group, dataId, data string) {
		uc.log.Infof("检测到配置变化: namespace=%s, group=%s, dataId=%s", namespace, group, dataId)

		// 异步处理配置变化
		go func() {
			err := uc.IncrementalSync(context.Background(), namespace, group, dataId, data)
			if err != nil {
				uc.log.Errorf("处理配置变化失败: %v", err)
			}
		}()
	})
}

// startPeriodicSync 启动定时同步
func (uc *ConfigSyncUseCase) startPeriodicSync(ctx context.Context) {
	uc.log.Infof("启动定时同步，间隔: %v", uc.config.ScanInterval)

	ticker := time.NewTicker(uc.config.ScanInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			uc.log.Info("执行定时全量同步")
			err := uc.FullSync(ctx)
			if err != nil {
				uc.log.Errorf("定时同步失败: %v", err)
			}
		case <-uc.stopChan:
			uc.log.Info("定时同步已停止")
			return
		case <-ctx.Done():
			uc.log.Info("上下文取消，定时同步已停止")
			return
		}
	}
}

// GetSyncStatus 获取同步状态
func (uc *ConfigSyncUseCase) GetSyncStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running":       uc.isRunning,
		"enable_auto_sync": uc.config.EnableAutoSync,
		"enable_listener":  uc.config.EnableListener,
		"scan_interval":    uc.config.ScanInterval.String(),
	}
}
