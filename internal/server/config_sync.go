package server

import (
	"context"

	"github.com/dsers/infra-nacos-config/internal/biz"
	"github.com/dsers/infra-kratos-common/pkg/log"
	"github.com/go-kratos/kratos/v2/transport"
)

// ConfigSyncServer 配置同步服务器
type ConfigSyncServer struct {
	uc  *biz.ConfigSyncUseCase
	log *log.Helper
	ctx context.Context
}

// NewConfigSyncServer 创建配置同步服务器
func NewConfigSyncServer(
	uc *biz.ConfigSyncUseCase,
	logger log.Logger,
) *ConfigSyncServer {
	return &ConfigSyncServer{
		uc:  uc,
		log: log.NewHelper(logger),
		ctx: context.Background(),
	}
}

// Start 启动服务器
func (s *ConfigSyncServer) Start(ctx context.Context) error {
	s.log.Info("启动配置同步服务器")
	s.ctx = ctx
	
	// 启动配置同步服务
	err := s.uc.Start(ctx)
	if err != nil {
		s.log.Errorf("启动配置同步服务失败: %v", err)
		return err
	}
	
	s.log.Info("配置同步服务器启动成功")
	return nil
}

// Stop 停止服务器
func (s *ConfigSyncServer) Stop(ctx context.Context) error {
	s.log.Info("停止配置同步服务器")
	
	s.uc.Stop()
	
	s.log.Info("配置同步服务器已停止")
	return nil
}

// Endpoint 返回服务端点（实现transport.Server接口）
func (s *ConfigSyncServer) Endpoint() (*transport.Endpoint, error) {
	return &transport.Endpoint{
		Scheme: "config-sync",
		Host:   "localhost",
	}, nil
}
