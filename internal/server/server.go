package server

import (
	v1common "github.com/dsers/infra-api-protocol/api/common/v1"
	pkgserver "github.com/dsers/infra-kratos-common/pkg/server"
	"github.com/google/wire"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(
	NewHTTPServer,
	NewGRPCServer,
	NewConfigSyncServer,
	InjectServer,
	pkgserver.NewProfilingServer,
	pkgserver.NewMetricServer,
)

func InjectServer(s *v1common.Server) pkgserver.IServer {
	return s
}
