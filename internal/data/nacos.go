package data

import (
	"context"
	"fmt"
	"sync"

	"github.com/dsers/infra-nacos-config/internal/conf"
	"github.com/dsers/infra-kratos-common/pkg/log"
	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
)

// NacosRepo Nacos仓库接口
type NacosRepo interface {
	GetAllConfigs(ctx context.Context) ([]conf.ConfigItem, error)
	ListenConfigChanges(ctx context.Context, callback func(namespace, group, dataId, data string)) error
	GetConfigsByNamespace(ctx context.Context, namespace string) ([]conf.ConfigItem, error)
}

type nacosRepo struct {
	config *conf.NacosConfig
	log    *log.Helper
	clients map[string]config_client.IConfigClient
	mu     sync.RWMutex
}

// NewNacosRepo 创建Nacos仓库
func NewNacosRepo(config *conf.NacosConfig, logger log.Logger) NacosRepo {
	return &nacosRepo{
		config:  config,
		log:     log.NewHelper(logger),
		clients: make(map[string]config_client.IConfigClient),
	}
}

// createServerConfig 创建服务器配置
func (r *nacosRepo) createServerConfig() []constant.ServerConfig {
	return []constant.ServerConfig{
		*constant.NewServerConfig(
			r.config.Server.Host,
			r.config.Server.Port,
			constant.WithContextPath(r.config.Server.ContextPath),
		),
	}
}

// createClientConfig 创建客户端配置
func (r *nacosRepo) createClientConfig(namespaceId string) constant.ClientConfig {
	return constant.ClientConfig{
		Endpoint:    r.config.Server.Endpoint,
		NamespaceId: namespaceId,
		RegionId:    r.config.Client.RegionId,
		AccessKey:   r.config.Client.AccessKey,
		SecretKey:   r.config.Client.SecretKey,
		OpenKMS:     r.config.Client.OpenKMS,
		TimeoutMs:   r.config.Client.TimeoutMs,
		LogLevel:    r.config.Client.LogLevel,
		LogDir:      r.config.Client.LogDir,
		CacheDir:    r.config.Client.CacheDir,
	}
}

// getOrCreateClient 获取或创建客户端
func (r *nacosRepo) getOrCreateClient(namespaceId string) (config_client.IConfigClient, error) {
	r.mu.RLock()
	if client, exists := r.clients[namespaceId]; exists {
		r.mu.RUnlock()
		return client, nil
	}
	r.mu.RUnlock()

	r.mu.Lock()
	defer r.mu.Unlock()

	// 双重检查
	if client, exists := r.clients[namespaceId]; exists {
		return client, nil
	}

	sc := r.createServerConfig()
	cc := r.createClientConfig(namespaceId)

	client, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &cc,
			ServerConfigs: sc,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("创建配置客户端失败: %v", err)
	}

	r.clients[namespaceId] = client
	return client, nil
}

// GetConfigsByNamespace 获取指定命名空间的所有配置
func (r *nacosRepo) GetConfigsByNamespace(ctx context.Context, namespace string) ([]conf.ConfigItem, error) {
	namespaceId, exists := r.config.Namespaces[namespace]
	if !exists {
		return nil, fmt.Errorf("命名空间 %s 不存在", namespace)
	}

	client, err := r.getOrCreateClient(namespaceId)
	if err != nil {
		return nil, err
	}

	// 使用分页获取所有配置
	pageSize := 100
	pageNo := 1
	var allConfigs []conf.ConfigItem

	for {
		configs, err := client.SearchConfig(vo.SearchConfigParam{
			Search:   "blur",
			Group:    "",
			PageNo:   pageNo,
			PageSize: pageSize,
		})
		if err != nil {
			return nil, fmt.Errorf("搜索配置失败: %v", err)
		}

		// 将搜索结果转换为我们自定义的ConfigItem结构
		for _, item := range configs.PageItems {
			allConfigs = append(allConfigs, conf.ConfigItem{
				DataId:    item.DataId,
				Group:     item.Group,
				Content:   item.Content,
				Namespace: namespace,
			})
		}

		// 如果已经获取了所有配置，则退出循环
		if pageNo*pageSize >= configs.TotalCount {
			break
		}

		pageNo++
	}

	r.log.Infof("命名空间 %s 中找到 %d 个配置", namespace, len(allConfigs))
	return allConfigs, nil
}

// GetAllConfigs 获取所有命名空间的配置
func (r *nacosRepo) GetAllConfigs(ctx context.Context) ([]conf.ConfigItem, error) {
	var allConfigs []conf.ConfigItem
	var wg sync.WaitGroup
	var mu sync.Mutex
	var errs []error

	// 为每个命名空间创建一个goroutine
	for namespaceName := range r.config.Namespaces {
		wg.Add(1)
		go func(name string) {
			defer wg.Done()

			configs, err := r.GetConfigsByNamespace(ctx, name)
			if err != nil {
				mu.Lock()
				errs = append(errs, fmt.Errorf("获取命名空间 %s 的配置失败: %v", name, err))
				mu.Unlock()
				return
			}

			mu.Lock()
			allConfigs = append(allConfigs, configs...)
			mu.Unlock()
		}(namespaceName)
	}

	wg.Wait()

	if len(errs) > 0 {
		return allConfigs, fmt.Errorf("部分命名空间获取失败: %v", errs)
	}

	return allConfigs, nil
}

// ListenConfigChanges 监听配置变化
func (r *nacosRepo) ListenConfigChanges(ctx context.Context, callback func(namespace, group, dataId, data string)) error {
	for namespaceName, namespaceId := range r.config.Namespaces {
		client, err := r.getOrCreateClient(namespaceId)
		if err != nil {
			return fmt.Errorf("创建客户端失败: %v", err)
		}

		// 获取当前命名空间的所有配置，为每个配置添加监听
		configs, err := r.GetConfigsByNamespace(ctx, namespaceName)
		if err != nil {
			r.log.Errorf("获取命名空间 %s 配置失败: %v", namespaceName, err)
			continue
		}

		for _, config := range configs {
			err := client.ListenConfig(vo.ConfigParam{
				DataId:   config.DataId,
				Group:    config.Group,
				OnChange: func(namespace, group, dataId, data string) {
					r.log.Infof("配置变化: namespace=%s, group=%s, dataId=%s", namespace, group, dataId)
					callback(namespaceName, group, dataId, data)
				},
			})
			if err != nil {
				r.log.Errorf("监听配置失败: %v", err)
			}
		}
	}

	return nil
}
