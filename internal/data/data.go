package data

import (
	"time"

	v1 "github.com/dsers/infra-api-protocol/api/infra-nacos-config/v1"
	"github.com/dsers/infra-nacos-config/internal/conf"
	"github.com/dsers/infra-kratos-common/pkg/log"
	"github.com/google/wire"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewModelRepo,
	NewNacosRepo,
	NewGitRepo,
	NewNacosConfig,
	NewGitConfig,
	NewConfigSyncConfig,
)

// Data .
type Data struct {
}

func NewData(bootstrap *v1.Bootstrap, logger log.Logger) (*Data, func(), error) {
	// 使用 github.com/dsers/infra-kratos-common/pkg/componet
	// 进行组件的初始化
	// 例如:
	// gorm.NewMySQLWithPanic(logger,bootstrap.MySQL)
	d := &Data{}

	cleanup := func() {
		// 使用 github.com/dsers/infra-kratos-common/pkg/componet
		// 进行组件的资源释放
		// 例如:
		// ctx, cancelFunc := context.WithTimeout(context.Background(), 10*time.Second)
		// defer cancelFunc()
		// gorm.Close(ctx,d.msyql)
	}

	return d, cleanup, nil
}

// NewNacosConfig 创建Nacos配置
func NewNacosConfig(bootstrap *v1.Bootstrap) *conf.NacosConfig {
	// 这里应该从bootstrap中读取配置，暂时使用硬编码
	return &conf.NacosConfig{
		Server: conf.NacosServerConfig{
			Endpoint:    "mse-257ca714-nacos-ans.mse.aliyuncs.com:8080",
			Host:        "mse-257ca714-nacos-ans.mse.aliyuncs.com",
			Port:        8848,
			ContextPath: "/nacos",
		},
		Client: conf.NacosClientConfig{
			RegionId:  "cn-zhangjiakou",
			AccessKey: "LTAI5tJuh5eLzb8qzGzWapoX",
			SecretKey: "******************************",
			OpenKMS:   true,
			TimeoutMs: 5000,
			LogLevel:  "debug",
			LogDir:    "./nacos/log",
			CacheDir:  "./nacos/cache",
		},
		Namespaces: map[string]string{
			"prod-app": "0e07a9b4-a03c-4226-85aa-272c36629ee8",
			"test":     "2df6698b-2dc1-43e4-9b18-5483b5411ec9",
			"prod":     "48850069-1a83-4996-8293-f5303ec38154",
			"staging":  "80264b14-a955-4804-a131-07a0b2b6297f",
		},
	}
}

// NewGitConfig 创建Git配置
func NewGitConfig(bootstrap *v1.Bootstrap) *conf.GitConfig {
	// 这里应该从bootstrap中读取配置，暂时使用硬编码
	return &conf.GitConfig{
		Repository: conf.GitRepositoryConfig{
			URL:       "https://github.com/dsers/nacos-config-yaml.git",
			LocalPath: "./nacos-config-yaml",
			Username:  "anshuo",
			Token:     "****************************************",
		},
		Proxy: conf.GitProxyConfig{
			URL: "http://127.0.0.1:7890",
		},
		Commit: conf.GitCommitConfig{
			AuthorName:  "Nacos Config Sync",
			AuthorEmail: "<EMAIL>",
		},
	}
}

// NewConfigSyncConfig 创建配置同步配置
func NewConfigSyncConfig(bootstrap *v1.Bootstrap) *conf.ConfigSyncConfig {
	// 这里应该从bootstrap中读取配置，暂时使用硬编码
	return &conf.ConfigSyncConfig{
		ScanInterval:   1 * time.Hour,
		EnableAutoSync: true,
		EnableListener: true,
	}
}
