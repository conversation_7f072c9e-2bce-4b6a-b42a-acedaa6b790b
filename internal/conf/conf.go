package conf

import "time"

// NacosConfig Nacos配置
type NacosConfig struct {
	Server     NacosServerConfig            `yaml:"server"`
	Client     NacosClientConfig            `yaml:"client"`
	Namespaces map[string]string            `yaml:"namespaces"`
}

// NacosServerConfig Nacos服务器配置
type NacosServerConfig struct {
	Endpoint    string `yaml:"endpoint"`
	Host        string `yaml:"host"`
	Port        uint64 `yaml:"port"`
	ContextPath string `yaml:"context_path"`
}

// NacosClientConfig Nacos客户端配置
type NacosClientConfig struct {
	RegionId  string `yaml:"region_id"`
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
	OpenKMS   bool   `yaml:"open_kms"`
	TimeoutMs uint64 `yaml:"timeout_ms"`
	LogLevel  string `yaml:"log_level"`
	LogDir    string `yaml:"log_dir"`
	CacheDir  string `yaml:"cache_dir"`
}

// GitConfig Git配置
type GitConfig struct {
	Repository GitRepositoryConfig `yaml:"repository"`
	Proxy      GitProxyConfig      `yaml:"proxy"`
	Commit     GitCommitConfig     `yaml:"commit"`
}

// GitRepositoryConfig Git仓库配置
type GitRepositoryConfig struct {
	URL       string `yaml:"url"`
	LocalPath string `yaml:"local_path"`
	Username  string `yaml:"username"`
	Token     string `yaml:"token"`
}

// GitProxyConfig Git代理配置
type GitProxyConfig struct {
	URL string `yaml:"url"`
}

// GitCommitConfig Git提交配置
type GitCommitConfig struct {
	AuthorName  string `yaml:"author_name"`
	AuthorEmail string `yaml:"author_email"`
}

// ConfigSyncConfig 配置同步设置
type ConfigSyncConfig struct {
	ScanInterval   time.Duration `yaml:"scan_interval"`
	EnableAutoSync bool          `yaml:"enable_auto_sync"`
	EnableListener bool          `yaml:"enable_listener"`
}

// ConfigItem 配置项结构
type ConfigItem struct {
	DataId    string `json:"data_id"`
	Group     string `json:"group"`
	Content   string `json:"content"`
	Namespace string `json:"namespace"`
	FilePath  string `json:"file_path"`
}
