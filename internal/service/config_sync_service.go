package service

import (
	"context"
	"encoding/json"

	"github.com/dsers/infra-nacos-config/internal/biz"
	"github.com/dsers/infra-kratos-common/pkg/log"
)

// ConfigSyncService 配置同步服务
type ConfigSyncService struct {
	uc  *biz.ConfigSyncUseCase
	log *log.Helper
}

// NewConfigSyncService 创建配置同步服务
func NewConfigSyncService(
	uc *biz.ConfigSyncUseCase,
	logger log.Logger,
) *ConfigSyncService {
	return &ConfigSyncService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// StartSync 启动同步服务
func (s *ConfigSyncService) StartSync(ctx context.Context) (map[string]interface{}, error) {
	s.log.WithContext(ctx).Info("收到启动同步服务请求")

	err := s.uc.Start(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("启动同步服务失败: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": err.<PERSON><PERSON>(),
		}, err
	}

	return map[string]interface{}{
		"success": true,
		"message": "同步服务启动成功",
	}, nil
}

// StopSync 停止同步服务
func (s *ConfigSyncService) StopSync(ctx context.Context) map[string]interface{} {
	s.log.WithContext(ctx).Info("收到停止同步服务请求")

	s.uc.Stop()

	return map[string]interface{}{
		"success": true,
		"message": "同步服务已停止",
	}
}

// TriggerFullSync 触发全量同步
func (s *ConfigSyncService) TriggerFullSync(ctx context.Context) (map[string]interface{}, error) {
	s.log.WithContext(ctx).Info("收到触发全量同步请求")

	err := s.uc.FullSync(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("全量同步失败: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": err.Error(),
		}, err
	}

	return map[string]interface{}{
		"success": true,
		"message": "全量同步完成",
	}, nil
}

// GetSyncStatus 获取同步状态
func (s *ConfigSyncService) GetSyncStatus(ctx context.Context) map[string]interface{} {
	s.log.WithContext(ctx).Info("收到获取同步状态请求")
	return s.uc.GetSyncStatus()
}
