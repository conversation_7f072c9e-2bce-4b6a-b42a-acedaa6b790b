package service

import (
	"context"
	"fmt"
	"os"

	v1 "github.com/dsers/infra-api-protocol/api/infra-nacos-config/v1"
	"github.com/dsers/infra-nacos-config/internal/biz"

	"github.com/dsers/infra-kratos-common/pkg/log"
)

// MainService is a main service.
type MainService struct {
	v1.UnimplementedMainServiceServer

	uc  *biz.MainServiceUseCase
	log *log.Helper
}

// NewMainService new a main service.
func NewMainService(
	uc *biz.MainServiceUseCase,
	logger log.Logger,
) *MainService {
	return &MainService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

func (s *MainService) HelloWorld(
	ctx context.Context,
	in *v1.HelloWorldRequest,
) (*v1.HelloWorldReply, error) {

	s.log.WithContext(ctx).Infof("Received Name: %v", in.GetName())

	var (
		hostname, _	= os.Hostname()
		sayHello	= fmt.Sprintf("Hello from %s, %s 🚀!", hostname, in.Name)
	)

	return &v1.HelloWorldReply{SayHello: sayHello}, nil
}
