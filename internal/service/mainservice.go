package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"

	v1 "github.com/dsers/infra-api-protocol/api/infra-nacos-config/v1"
	"github.com/dsers/infra-nacos-config/internal/biz"

	"github.com/dsers/infra-kratos-common/pkg/log"
)

// MainService is a main service.
type MainService struct {
	v1.UnimplementedMainServiceServer

	uc        *biz.MainServiceUseCase
	configSvc *ConfigSyncService
	log       *log.Helper
}

// NewMainService new a main service.
func NewMainService(
	uc *biz.MainServiceUseCase,
	configSvc *ConfigSyncService,
	logger log.Logger,
) *MainService {
	return &MainService{
		uc:        uc,
		configSvc: configSvc,
		log:       log.NewHelper(logger),
	}
}

func (s *MainService) HelloWorld(
	ctx context.Context,
	in *v1.HelloWorldRequest,
) (*v1.HelloWorldReply, error) {

	s.log.WithContext(ctx).Infof("Received Name: %v", in.GetName())

	var (
		hostname, _	= os.Hostname()
		sayHello	= fmt.Sprintf("Hello from %s, %s 🚀!", hostname, in.Name)
	)

	return &v1.HelloWorldReply{SayHello: sayHello}, nil
}

// ConfigSyncHandler 配置同步HTTP处理器
func (s *MainService) ConfigSyncHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		switch r.Method {
		case http.MethodPost:
			switch r.URL.Path {
			case "/api/config-sync/start":
				s.handleStartSync(w, r)
			case "/api/config-sync/stop":
				s.handleStopSync(w, r)
			case "/api/config-sync/trigger-full-sync":
				s.handleTriggerFullSync(w, r)
			default:
				http.NotFound(w, r)
			}
		case http.MethodGet:
			switch r.URL.Path {
			case "/api/config-sync/status":
				s.handleGetSyncStatus(w, r)
			default:
				http.NotFound(w, r)
			}
		default:
			http.MethodNotAllowed(w, r)
		}
	}
}

func (s *MainService) handleStartSync(w http.ResponseWriter, r *http.Request) {
	result, err := s.configSvc.StartSync(r.Context())
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
	}
	json.NewEncoder(w).Encode(result)
}

func (s *MainService) handleStopSync(w http.ResponseWriter, r *http.Request) {
	result := s.configSvc.StopSync(r.Context())
	json.NewEncoder(w).Encode(result)
}

func (s *MainService) handleTriggerFullSync(w http.ResponseWriter, r *http.Request) {
	result, err := s.configSvc.TriggerFullSync(r.Context())
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
	}
	json.NewEncoder(w).Encode(result)
}

func (s *MainService) handleGetSyncStatus(w http.ResponseWriter, r *http.Request) {
	result := s.configSvc.GetSyncStatus(r.Context())
	json.NewEncoder(w).Encode(result)
}
