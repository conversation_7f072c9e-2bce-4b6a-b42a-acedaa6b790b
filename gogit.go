package main

import (
	"fmt"
	"github.com/go-git/go-git/v6"
	"github.com/go-git/go-git/v6/plumbing/object"
	"github.com/go-git/go-git/v6/plumbing/transport"
	"github.com/go-git/go-git/v6/plumbing/transport/http"
	"os"
	"time"
)

func main() {
	directory := "./nacos-config-yaml"
	token := "****************************************"
	url := "https://github.com/dsers/nacos-config-yaml.git"
	httpProxyUrl := "http://127.0.0.1:7890"
	// Clone the given repository to the given directory

	r, err := git.PlainClone(directory, &git.CloneOptions{
		// The intended use of a GitHub personal access token is in replace of your password
		// because access tokens can easily be revoked.
		// https://help.github.com/articles/creating-a-personal-access-token-for-the-command-line/
		ProxyOptions: transport.ProxyOptions{
			URL: httpProxyUrl,
		},
		Auth: &http.BasicAuth{
			Username: "anshuo", // yes, this can be anything except an empty string
			Password: token,
		},
		URL:      url,
		Progress: os.Stdout,
	})
	//r, err := git.PlainOpen(directory)
	fmt.Println(err)
	return
	w, err := r.Worktree()
	fmt.Println(fmt.Sprintf("%+v,%+v", w, err))
	os.WriteFile(directory+"/test.go", []byte("package main"), 0644)

	// Adds the new file to the staging area.
	_, err = w.Add("test.go")
	fmt.Println(fmt.Sprintf("%+v", err))

	// We can verify the current status of the worktree using the method Status.
	status, err := w.Status()
	fmt.Println(fmt.Sprintf("%+v,%+v", status, err))

	// Commits the current staging area to the repository, with the new file
	// just created. We should provide the object.Signature of Author of the
	// commit Since version 5.0.1, we can omit the Author signature, being read
	// from the git config files.
	commit, err := w.Commit("example go-git commit", &git.CommitOptions{
		Author: &object.Signature{
			Name:  "John Doe",
			Email: "<EMAIL>",
			When:  time.Now(),
		},
	})
	fmt.Println(fmt.Sprintf("%+v,%+v", commit, err))

	// Prints the current HEAD to verify that all worked well.
	obj, err := r.CommitObject(commit)
	fmt.Println(fmt.Sprintf("%+v,%+v", obj, err))

	err = r.Push(&git.PushOptions{
		RemoteName: "origin",
		Auth: &http.BasicAuth{
			Username: "anshuo", // yes, this can be anything except an empty string
			Password: token,
		},
	})
	fmt.Println(fmt.Sprintf("%+v", err))

}
