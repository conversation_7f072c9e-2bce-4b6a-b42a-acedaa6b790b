package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/dsers/infra-nacos-config/internal/biz"
	"github.com/dsers/infra-nacos-config/internal/conf"
	"github.com/dsers/infra-nacos-config/internal/data"
	"github.com/dsers/infra-nacos-config/internal/service"
	kratoslog "github.com/dsers/infra-kratos-common/pkg/log"
)

func main() {
	// 创建logger
	logger := kratoslog.NewStdLogger(log.Writer())

	// 创建配置
	nacosConfig := &conf.NacosConfig{
		Server: conf.NacosServerConfig{
			Endpoint:    "mse-257ca714-nacos-ans.mse.aliyuncs.com:8080",
			Host:        "mse-257ca714-nacos-ans.mse.aliyuncs.com",
			Port:        8848,
			ContextPath: "/nacos",
		},
		Client: conf.NacosClientConfig{
			RegionId:  "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>",
			AccessKey: "LTAI5tJuh5eLzb8qzGzWapoX",
			SecretKey: "******************************",
			OpenKMS:   true,
			TimeoutMs: 5000,
			LogLevel:  "debug",
			LogDir:    "./nacos/log",
			CacheDir:  "./nacos/cache",
		},
		Namespaces: map[string]string{
			"prod-app": "0e07a9b4-a03c-4226-85aa-272c36629ee8",
			"test":     "2df6698b-2dc1-43e4-9b18-5483b5411ec9",
			"prod":     "48850069-1a83-4996-8293-f5303ec38154",
			"staging":  "80264b14-a955-4804-a131-07a0b2b6297f",
		},
	}

	gitConfig := &conf.GitConfig{
		Repository: conf.GitRepositoryConfig{
			URL:       "https://github.com/dsers/nacos-config-yaml.git",
			LocalPath: "./nacos-config-yaml",
			Username:  "anshuo",
			Token:     "****************************************",
		},
		Proxy: conf.GitProxyConfig{
			URL: "http://127.0.0.1:7890",
		},
		Commit: conf.GitCommitConfig{
			AuthorName:  "Nacos Config Sync",
			AuthorEmail: "<EMAIL>",
		},
	}

	syncConfig := &conf.ConfigSyncConfig{
		ScanInterval:   10 * time.Second, // 测试用，设置为10秒
		EnableAutoSync: true,
		EnableListener: true,
	}

	// 创建仓库
	nacosRepo := data.NewNacosRepo(nacosConfig, logger)
	gitRepo := data.NewGitRepo(gitConfig, logger)

	// 创建用例
	configSyncUC := biz.NewConfigSyncUseCase(nacosRepo, gitRepo, syncConfig, logger)

	// 创建服务
	configSyncSvc := service.NewConfigSyncService(configSyncUC, logger)

	// 测试启动同步
	ctx := context.Background()
	fmt.Println("开始测试配置同步...")

	result, err := configSyncSvc.StartSync(ctx)
	if err != nil {
		fmt.Printf("启动同步失败: %v\n", err)
		return
	}

	fmt.Printf("启动同步结果: %+v\n", result)

	// 等待一段时间让同步运行
	fmt.Println("等待同步运行...")
	time.Sleep(15 * time.Second)

	// 测试增量同步（模拟配置变化）
	fmt.Println("测试增量同步...")
	err = configSyncUC.IncrementalSync(ctx, "test", "application", "test-config", "test: value1")
	if err != nil {
		fmt.Printf("增量同步失败: %v\n", err)
	}

	// 再次同步相同内容（应该跳过）
	fmt.Println("测试相同内容同步（应该跳过）...")
	err = configSyncUC.IncrementalSync(ctx, "test", "application", "test-config", "test: value1")
	if err != nil {
		fmt.Printf("增量同步失败: %v\n", err)
	}

	// 同步不同内容（应该执行）
	fmt.Println("测试不同内容同步（应该执行）...")
	err = configSyncUC.IncrementalSync(ctx, "test", "application", "test-config", "test: value2")
	if err != nil {
		fmt.Printf("增量同步失败: %v\n", err)
	}

	// 获取状态
	status := configSyncSvc.GetSyncStatus(ctx)
	fmt.Printf("同步状态: %+v\n", status)

	// 停止同步
	stopResult := configSyncSvc.StopSync(ctx)
	fmt.Printf("停止同步结果: %+v\n", stopResult)

	fmt.Println("测试完成")
}
